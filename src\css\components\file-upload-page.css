/**
 * @file 文件上传主页样式
 * @description iOS风格的文件上传主页界面样式
 */

/* 文件上传主页容器 */
#fileUploadPage {
    background: var(--ios-background-primary);
}

/* 欢迎区域 */
.welcome-section {
    text-align: center;
    padding: 32px 20px 24px;
    background: linear-gradient(135deg, var(--ios-blue) 0%, var(--ios-blue-dark) 100%);
    color: white;
    margin-bottom: 24px;
}

.welcome-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-icon svg {
    width: 32px;
    height: 32px;
    fill: white;
}

.welcome-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px;
    color: white;
}

.welcome-description {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
}

/* 文件上传区域 */
.file-upload-section {
    padding: 0 20px 24px;
}

#fileUploadContainer {
    background: var(--ios-background-secondary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 已保存配置区域 */
.saved-configs-section {
    padding: 0 20px 24px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--ios-text-primary);
    margin: 0;
}

.section-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--ios-blue);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    z-index: 10;
}

.section-action-btn:hover {
    background: var(--ios-blue-dark);
    transform: scale(1.05);
}

.section-action-btn:active {
    transform: scale(0.95);
    background: var(--ios-blue-darker);
}

.section-action-btn svg {
    width: 18px;
    height: 18px;
    fill: white;
}

/* 配置预览列表 */
.config-preview-list {
    display: grid;
    gap: 12px;
}

.config-preview-card {
    background: var(--ios-background-secondary);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid var(--ios-separator);
    transition: all 0.2s ease;
    cursor: pointer;
}

.config-preview-card:hover {
    background: var(--ios-background-tertiary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-preview-card:active {
    transform: translateY(0);
    background: var(--ios-background-quaternary);
}

.config-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.config-preview-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--ios-text-primary);
    margin: 0;
}

.config-preview-actions {
    display: flex;
    gap: 8px;
}

.config-preview-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--ios-gray-light);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.config-preview-btn:hover {
    background: var(--ios-gray);
}

.config-preview-btn:active {
    transform: scale(0.9);
}

.config-preview-btn svg {
    width: 14px;
    height: 14px;
    fill: var(--ios-text-secondary);
}

.config-preview-btn.edit-btn:hover {
    background: var(--ios-blue);
}

.config-preview-btn.edit-btn:hover svg {
    fill: white;
}

.config-preview-btn.delete-btn:hover {
    background: var(--ios-red);
}

.config-preview-btn.delete-btn:hover svg {
    fill: white;
}

.config-preview-description {
    font-size: 14px;
    color: var(--ios-text-secondary);
    margin: 0;
    line-height: 1.4;
}

.config-preview-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 8px;
    font-size: 12px;
    color: var(--ios-text-tertiary);
}

.config-preview-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

.config-preview-fields {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 空状态 */
.empty-configs-state {
    text-align: center;
    padding: 32px 20px;
    background: var(--ios-background-secondary);
    border-radius: 12px;
    border: 1px solid var(--ios-separator);
}

.empty-configs-state .empty-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    background: var(--ios-gray-light);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-configs-state .empty-icon svg {
    width: 24px;
    height: 24px;
    fill: var(--ios-text-tertiary);
}

.empty-configs-state .empty-description {
    font-size: 14px;
    color: var(--ios-text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .welcome-section {
        padding: 24px 16px 20px;
    }
    
    .welcome-title {
        font-size: 20px;
    }
    
    .welcome-description {
        font-size: 14px;
    }
    
    .file-upload-section,
    .saved-configs-section {
        padding: 0 16px 20px;
    }
    
    .section-title {
        font-size: 16px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .welcome-section {
        background: linear-gradient(135deg, var(--ios-blue-dark) 0%, var(--ios-blue-darker) 100%);
    }
    
    .config-preview-card {
        border-color: var(--ios-separator-dark);
    }
    
    .empty-configs-state {
        border-color: var(--ios-separator-dark);
    }
}
