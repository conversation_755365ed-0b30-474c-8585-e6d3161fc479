# GoMyHire 移动端快速透视分析 - 命名规范文档

## 项目命名规范总览

### 全局命名空间结构
- **主命名空间**: `SmartOffice` - 全局应用命名空间
- **模块前缀**: 所有组件和功能使用 `smartoffice-` 前缀
- **文件命名**: 小写字母 + 连字符，如 `smartoffice-config-list.js`
- **构造函数**: 大驼峰命名，如 `ConfigListComponent`
- **方法和属性**: 小驼峰命名，如 `loadConfigs`

## 核心模块命名表

### SmartOffice.Core - 核心基础设施
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Core.EventBus | 类 | 全局事件总线系统 | src/js/core/smartoffice-events.js | 无 |
| SmartOffice.Core.Storage | 类 | 本地存储管理器 | src/js/core/smartoffice-storage.js | 无 |
| SmartOffice.Core.Router | 类 | 页面路由管理器 | src/js/core/smartoffice-router.js | EventBus, DOM |
| SmartOffice.Core.App | 类 | 应用主控制器 | src/js/core/smartoffice-app.js | 所有核心模块 |

### SmartOffice.Components - UI组件
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Components.ConfigList | 类 | 配置列表组件 | src/js/components/smartoffice-config-list.js | Storage, EventBus, DOM |
| SmartOffice.Components.ConfigForm | 类 | 配置表单组件 | src/js/components/smartoffice-config-form.js | Storage, EventBus, DOM |
| SmartOffice.Components.Dropdown | 类 | 移动端下拉菜单 | src/js/components/smartoffice-dropdown.js | DOM, Events |
| SmartOffice.Components.DataTable | 类 | 数据表格显示组件 | src/js/components/smartoffice-data-table.js | DOM, Format |
| SmartOffice.Components.FileUpload | 类 | 文件上传组件 | src/js/components/smartoffice-file-upload.js | EventBus, DataValidator |
| SmartOffice.Components.FieldSelector | 类 | 字段选择器组件 | src/js/components/smartoffice-field-selector.js | DOM, Events |
| SmartOffice.Components.Loading | 类 | 加载状态组件 | src/js/components/smartoffice-loading.js | DOM |

### SmartOffice.Data - 数据处理模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Data.CSVParser | 类 | CSV文件解析器 | src/js/data/smartoffice-csv-parser.js | DataValidator |
| SmartOffice.Data.PivotEngine | 类 | 透视表计算引擎 | src/js/data/smartoffice-pivot-engine.js | Helpers |
| SmartOffice.Data.ConfigManager | 类 | 配置管理器 | src/js/data/smartoffice-config-manager.js | Storage |
| SmartOffice.Data.DataValidator | 类 | 数据验证器 | src/js/data/smartoffice-data-validator.js | 无 |

### SmartOffice.Utils - 工具函数模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Utils.Helpers | 对象 | 通用工具函数集合 | src/js/utils/smartoffice-helpers.js | 无 |
| SmartOffice.Utils.DOM | 对象 | DOM操作工具集合 | src/js/utils/smartoffice-dom.js | 无 |
| SmartOffice.Utils.Format | 对象 | 数据格式化工具 | src/js/utils/smartoffice-format.js | 无 |

### SmartOffice.Workers - 离线功能模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Workers.ServiceWorker | 对象 | Service Worker管理 | src/js/workers/smartoffice-service-worker.js | 无 |
| SmartOffice.Core.OfflineManager | 类 | 离线功能管理器 | src/js/core/smartoffice-offline.js | EventBus |

### SmartOffice.Templates - 模板管理模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Templates.TemplateManager | 类 | 模板管理器 | src/js/templates/smartoffice-template-manager.js | Storage, EventBus |

### SmartOffice.Parsers - 文件解析器模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Parsers.ExcelParser | 类 | Excel文件解析器 | src/js/parsers/smartoffice-excel-parser.js | DataValidator, EventBus |
| SmartOffice.Parsers.JSONParser | 类 | JSON文件解析器 | src/js/parsers/smartoffice-json-parser.js | DataValidator, EventBus |

### SmartOffice.Filters - 数据筛选模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Filters.DataFilters | 类 | 数据筛选器 | src/js/filters/smartoffice-data-filters.js | EventBus, Helpers |

### SmartOffice.Visualization - 可视化模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Visualization.Formatter | 类 | 条件格式化器 | src/js/visualization/smartoffice-formatter.js | EventBus, Helpers |
| SmartOffice.Visualization.ChartEngine | 类 | 图表引擎 | src/js/visualization/smartoffice-chart-engine.js | EventBus, Helpers |

## 事件命名规范

### SmartOffice.Events - 标准事件名称
| 事件名称 | 用途 | 触发时机 | 参数 |
|----------|------|----------|------|
| CONFIG_CREATE | 配置创建 | 新建配置保存时 | config对象 |
| CONFIG_UPDATE | 配置更新 | 编辑配置保存时 | config对象 |
| CONFIG_DELETE | 配置删除 | 删除配置时 | configId |
| FILE_UPLOAD_START | 文件上传开始 | 选择文件后 | file对象 |
| FILE_UPLOAD_PROGRESS | 文件上传进度 | 上传过程中 | progress百分比 |
| FILE_UPLOAD_COMPLETE | 文件上传完成 | 文件解析完成 | 解析后的数据 |
| DATA_PARSE_START | 数据解析开始 | 开始解析文件 | file对象 |
| DATA_PARSE_COMPLETE | 数据解析完成 | 解析完成 | 解析结果 |
| PIVOT_CALCULATE_START | 透视表计算开始 | 开始计算 | config对象 |
| PIVOT_CALCULATE_COMPLETE | 透视表计算完成 | 计算完成 | 结果数据 |
| TIME_RANGE_UPDATED | 时间区间更新 | 时间区间配置完成 | {field, timeRanges} |
| AGGREGATION_UPDATED | 聚合方式更新 | 聚合方式配置完成 | {field, aggregations} |

## 存储键名规范

### SmartOffice.StorageKeys - 标准存储键
| 键名 | 用途 | 数据类型 | 示例值 |
|------|------|----------|--------|
| configs | 透视表配置列表 | Array | [config1, config2] |
| currentConfig | 当前编辑配置 | Object | {id, name, fields} |
| appSettings | 应用设置 | Object | {theme, language} |
| userPrefs | 用户偏好 | Object | {autoSave, notifications} |
| lastUpload | 最后上传数据信息 | Object | {filename, timestamp} |

## CSS类命名规范

### 组件样式类
- **前缀**: 所有样式类使用 `so-` 前缀 (SmartOffice缩写)
- **组件类**: `so-config-list`, `so-config-form`, `so-dropdown`
- **状态类**: `so-active`, `so-disabled`, `so-loading`, `so-error`
- **iOS风格类**: `so-ios-card`, `so-ios-button`, `so-ios-input`

### 页面和布局类
- **页面类**: `page`, `page-hidden`, `page-content`
- **导航类**: `nav-bar`, `nav-title`, `nav-button`, `nav-back`, `nav-add`
- **容器类**: `app-container`, `main-content`, `config-list`

## 函数命名规范

### 生命周期函数
- **初始化**: `init()` - 组件初始化
- **渲染**: `render()` - 渲染界面
- **销毁**: `destroy()` - 清理资源
- **事件绑定**: `bindEvents()` - 绑定事件监听器

### 数据操作函数
- **加载**: `load*()` - 如 `loadConfigs()`, `loadData()`
- **保存**: `save*()` - 如 `saveConfig()`, `saveData()`
- **创建**: `create*()` - 如 `createConfig()`, `createCard()`
- **更新**: `update*()` - 如 `updateConfig()`, `updateUI()`
- **删除**: `delete*()` - 如 `deleteConfig()`, `deleteCard()`

### 事件处理函数
- **点击事件**: `on*Click()` - 如 `onAddClick()`, `onSaveClick()`
- **变更事件**: `on*Change()` - 如 `onFieldChange()`, `onValueChange()`
- **页面事件**: `onEnter*()`, `onLeave*()` - 如 `onEnterConfigForm()`

## 变量命名规范

### 实例变量
- **DOM元素**: `*Element` - 如 `containerElement`, `buttonElement`
- **数据集合**: `*s` - 如 `configs`, `fields`, `results`
- **当前项**: `current*` - 如 `currentConfig`, `currentData`
- **状态标志**: `is*` - 如 `isLoading`, `isEditing`, `isValid`

### 配置对象属性
- **基础信息**: `id`, `name`, `description`, `createdAt`, `updatedAt`
- **字段配置**: `rowFields`, `columnFields`, `valueFields`, `filterFields`
- **聚合设置**: `aggregationType` - 值为 'sum', 'count', 'average', 'min', 'max'

## 防重复命名检查清单

### 禁止使用的通用词汇
- ❌ `unified`, `mapping`, `converter`, `helper` (过于通用)
- ❌ `manager`, `handler`, `processor` (除非有明确业务前缀)
- ❌ `utils`, `common`, `shared` (必须加业务前缀)

### 推荐的业务前缀
- ✅ `config*` - 配置相关功能
- ✅ `pivot*` - 透视表相关功能
- ✅ `file*` - 文件处理相关功能
- ✅ `data*` - 数据处理相关功能
- ✅ `ui*` - 界面相关功能

## 更新记录

- **2025-01-03**: 初始创建命名规范文档
- **2025-01-03**: 记录所有已实现组件的命名规范
- **2025-01-03**: 建立事件、存储、CSS类的命名标准
- **2025-01-03**: 添加字段选择器组件命名规范，完成95%组件的命名记录
- **2025-01-03 增强功能更新**: 添加6个增强功能模块的命名规范，包括离线功能、模板管理、文件解析器、数据筛选、条件格式化、数据可视化
- **2025-01-03 功能增强完成**: 添加返回主页按钮和值字段多重聚合功能的命名规范
- **2025-06-16 路由问题修复**: 添加文件上传主页、配置预览组件、路由重构相关命名规范

## 新增功能命名记录 (2025-01-03)

### 返回主页按钮功能相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| form-title-container | CSS类 | 标题容器布局 | src/css/components/config-form.css | 无 |
| back-to-home-button | CSS类 | 返回主页按钮样式 | src/css/components/config-form.css | 无 |
| back-icon | CSS类 | 返回按钮图标样式 | src/css/components/config-form.css | 无 |
| title-spacer | CSS类 | 标题占位元素 | src/css/components/config-form.css | 无 |
| handleBackToHome | 方法 | 返回主页处理逻辑 | src/js/components/smartoffice-config-form.js | Router |

### 值字段多重聚合功能相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| getAggregationLabel | 方法 | 获取聚合类型中文标签 | src/js/data/smartoffice-pivot-engine.js | 无 |
| so-aggregation-btn | CSS类 | 聚合方式设置按钮 | src/css/components/field-selector.css | 无 |
| aggregation-modal | CSS类 | 聚合方式模态框 | src/css/components/field-selector.css | 无 |
| aggregation-options-grid | CSS类 | 聚合选项网格布局 | src/css/components/field-selector.css | 无 |
| showAggregationModal | 方法 | 显示聚合方式模态框 | src/js/components/smartoffice-field-selector.js | DOM |
| createAggregationModal | 方法 | 创建聚合方式模态框 | src/js/components/smartoffice-field-selector.js | DOM |
| toggleAggregationOption | 方法 | 切换聚合选项 | src/js/components/smartoffice-field-selector.js | 无 |
| confirmAggregations | 方法 | 确认聚合方式设置 | src/js/components/smartoffice-field-selector.js | EventBus |
| handleAggregationUpdate | 方法 | 处理聚合方式更新 | src/js/components/smartoffice-config-form.js | 无 |
| AGGREGATION_UPDATED | 事件常量 | 聚合方式更新事件 | src/js/core/smartoffice-core.js | 无 |

## 路由问题修复命名记录 (2025-06-16)

### 文件上传主页相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| fileUploadPage | HTML元素ID | 文件上传主页容器 | index.html | 无 |
| welcome-section | CSS类 | 欢迎区域样式 | src/css/components/file-upload-page.css | 无 |
| welcome-title | CSS类 | 欢迎标题样式 | src/css/components/file-upload-page.css | 无 |
| welcome-description | CSS类 | 欢迎描述样式 | src/css/components/file-upload-page.css | 无 |
| file-upload-section | CSS类 | 文件上传区域样式 | src/css/components/file-upload-page.css | 无 |
| saved-configs-section | CSS类 | 已保存配置区域样式 | src/css/components/file-upload-page.css | 无 |
| section-action-btn | CSS类 | 区域操作按钮样式 | src/css/components/file-upload-page.css | 无 |
| manageConfigsBtn | HTML元素ID | 管理配置按钮 | index.html | 无 |

### 配置预览组件相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Components.ConfigPreview | 类 | 配置预览组件 | src/js/components/smartoffice-config-preview.js | ConfigManager, EventBus |
| config-preview-list | CSS类 | 配置预览列表样式 | src/css/components/file-upload-page.css | 无 |
| config-preview-card | CSS类 | 配置预览卡片样式 | src/css/components/file-upload-page.css | 无 |
| config-preview-header | CSS类 | 配置预览头部样式 | src/css/components/file-upload-page.css | 无 |
| config-preview-name | CSS类 | 配置名称样式 | src/css/components/file-upload-page.css | 无 |
| config-preview-actions | CSS类 | 配置操作按钮组样式 | src/css/components/file-upload-page.css | 无 |
| config-preview-btn | CSS类 | 配置操作按钮样式 | src/css/components/file-upload-page.css | 无 |
| empty-configs-state | CSS类 | 空配置状态样式 | src/css/components/file-upload-page.css | 无 |
| loadConfigs | 方法 | 加载配置数据 | src/js/components/smartoffice-config-preview.js | ConfigManager |
| renderConfigList | 方法 | 渲染配置列表 | src/js/components/smartoffice-config-preview.js | DOM |
| createConfigCard | 方法 | 创建配置卡片 | src/js/components/smartoffice-config-preview.js | DOM |
| handleEditConfig | 方法 | 处理编辑配置 | src/js/components/smartoffice-config-preview.js | EventBus |
| handleDeleteConfig | 方法 | 处理删除配置 | src/js/components/smartoffice-config-preview.js | ConfigManager |

### 配置管理器完整实现相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| getAllConfigs | 方法 | 获取所有配置 | src/js/data/smartoffice-config-manager.js | Storage |
| getConfigById | 方法 | 根据ID获取配置 | src/js/data/smartoffice-config-manager.js | Storage |
| saveConfig | 方法 | 保存配置 | src/js/data/smartoffice-config-manager.js | Storage, Helpers |
| deleteConfig | 方法 | 删除配置 | src/js/data/smartoffice-config-manager.js | Storage |
| duplicateConfig | 方法 | 复制配置 | src/js/data/smartoffice-config-manager.js | Storage, Helpers |
| storageKey | 属性 | 存储键名 | src/js/data/smartoffice-config-manager.js | 无 |

### 路由系统重构相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| home | 路由名称 | 文件上传主页路由 | src/js/core/smartoffice-router.js | 无 |
| initializeRouter | 方法 | 初始化路由系统 | src/js/core/smartoffice-app.js | Router |
| handleManageConfigs | 方法 | 处理管理配置按钮点击 | src/js/core/smartoffice-app.js | Router |
| fileUploadContainer | 属性 | 文件上传容器元素引用 | src/js/core/smartoffice-app.js | DOM |
| configPreviewList | 属性 | 配置预览列表元素引用 | src/js/core/smartoffice-app.js | DOM |
| emptyConfigsState | 属性 | 空配置状态元素引用 | src/js/core/smartoffice-app.js | DOM |

### 事件处理优化相关命名
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| configPreview:refresh | 事件名称 | 配置预览刷新事件 | src/js/components/smartoffice-config-preview.js | EventBus |
| fileUpload:init | 事件名称 | 文件上传初始化事件 | src/js/core/smartoffice-router.js | EventBus |
